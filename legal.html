<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>IMPAXX - Legal & Support</title>

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    <link rel="apple-touch-icon" href="/public/icons/apple-icon.png">
    <link rel="manifest" href="/manifest.json">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            overflow-x: hidden;
            background: linear-gradient(135deg, #0f0f14 0%, #12141a 25%, #1a1d26 50%, #1e212a 75%, #242832 100%);
            cursor: none;
            color: #fff;
            line-height: 1.6;
        }

        /* Enhanced custom cursor */
        .cursor {
            position: fixed;
            width: 20px;
            height: 20px;
            background: radial-gradient(circle, rgba(168, 162, 158, 0.9) 0%, rgba(168, 162, 158, 0.4) 40%, transparent 70%);
            border: 1px solid rgba(168, 162, 158, 0.7);
            border-radius: 50%;
            pointer-events: none;
            z-index: 9999;
            transition: all 0.08s ease-out;
            box-shadow: 0 0 15px rgba(168, 162, 158, 0.3), inset 0 0 8px rgba(168, 162, 158, 0.2);
        }

        .cursor.active {
            transform: scale(1.3);
            background: radial-gradient(circle, rgba(168, 162, 158, 1) 0%, rgba(168, 162, 158, 0.6) 40%, transparent 70%);
            border-color: rgba(168, 162, 158, 0.9);
            box-shadow: 0 0 20px rgba(168, 162, 158, 0.4), inset 0 0 12px rgba(168, 162, 158, 0.3);
        }

        /* Navigation */
        .nav {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            background: rgba(0, 0, 0, 0.7);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            padding: 1rem 0;
            transition: all 0.3s ease;
        }

        .nav::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(90deg, 
                rgba(139, 92, 246, 0.1) 0%, 
                rgba(6, 182, 212, 0.1) 50%, 
                rgba(139, 92, 246, 0.1) 100%);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .nav.scrolled {
            background: rgba(0, 0, 0, 0.95);
            padding: 0.5rem 0;
        }

        .nav.scrolled::before {
            opacity: 1;
        }

        .nav-container {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 2rem;
        }

        .logo {
            font-size: 1.8rem;
            font-weight: 800;
            letter-spacing: 0.1em;
            text-transform: uppercase;
            color: #fff !important;
            text-shadow: 0 0 10px rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
            position: relative;
            text-decoration: none;
        }

        .logo::after {
            content: '';
            position: absolute;
            bottom: -5px;
            left: 0;
            width: 100%;
            height: 2px;
            background: linear-gradient(90deg, rgba(255, 255, 255, 0.8), rgba(255, 255, 255, 0.4));
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }

        .logo:hover::after {
            transform: scaleX(1);
        }

        .nav-links {
            display: flex;
            gap: 2rem;
            list-style: none;
        }

        .nav-links a {
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
            padding: 0.5rem 1rem;
            border-radius: 25px;
            position: relative;
        }

        .nav-links a::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 25px;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .nav-links a:hover::before {
            opacity: 1;
        }

        .nav-links a:hover {
            color: #fff;
            transform: translateY(-2px);
        }

        /* Mobile menu button */
        .mobile-menu-btn {
            display: none;
            background: none;
            border: none;
            color: white;
            font-size: 1.5rem;
            cursor: pointer;
        }

        /* Main content */
        .main-content {
            margin-top: 80px;
            min-height: calc(100vh - 80px);
        }

        /* Hero section for legal page */
        .legal-hero {
            position: relative;
            padding: 6rem 2rem 4rem;
            text-align: center;
            background: 
                linear-gradient(135deg, rgba(26, 29, 38, 0.8) 0%, rgba(30, 33, 42, 0.9) 50%, rgba(36, 40, 50, 0.8) 100%),
                radial-gradient(ellipse at 20% 30%, rgba(45, 55, 72, 0.1) 0%, transparent 50%),
                radial-gradient(ellipse at 80% 70%, rgba(55, 65, 81, 0.1) 0%, transparent 50%),
                linear-gradient(180deg, #1a1d26 0%, #242832 100%);
        }

        .legal-hero h1 {
            font-size: clamp(2.5rem, 5vw, 4rem);
            color: #ffffff;
            font-weight: 300;
            margin-bottom: 1.5rem;
            line-height: 1.2;
        }

        .accent-word {
            font-weight: 700;
            position: relative;
        }

        .accent-word::after {
            content: '';
            position: absolute;
            bottom: -4px;
            left: 0;
            width: 100%;
            height: 1px;
            background: linear-gradient(90deg, transparent, rgba(168, 162, 158, 0.6), transparent);
            opacity: 0.7;
        }

        .legal-hero p {
            font-size: 1.2rem;
            color: rgba(255, 255, 255, 0.7);
            font-weight: 300;
            max-width: 600px;
            margin: 0 auto;
        }

        /* Legal sections */
        .legal-section {
            padding: 4rem 2rem;
            background: 
                linear-gradient(135deg, rgba(30, 33, 42, 0.98) 0%, rgba(36, 40, 50, 0.95) 50%, rgba(42, 48, 60, 0.98) 100%),
                radial-gradient(ellipse at 30% 40%, rgba(55, 65, 81, 0.06) 0%, transparent 70%),
                radial-gradient(ellipse at 70% 60%, rgba(65, 75, 91, 0.06) 0%, transparent 70%);
        }

        .legal-section:nth-child(even) {
            background: 
                linear-gradient(135deg, rgba(26, 29, 38, 0.95) 0%, rgba(30, 33, 42, 0.98) 50%, rgba(36, 40, 50, 0.95) 100%),
                radial-gradient(ellipse at 10% 20%, rgba(45, 55, 72, 0.08) 0%, transparent 60%),
                radial-gradient(ellipse at 90% 80%, rgba(55, 65, 81, 0.08) 0%, transparent 60%);
        }

        .section-container {
            max-width: 1000px;
            margin: 0 auto;
        }

        .section-header {
            margin-bottom: 3rem;
            position: relative;
        }

        .section-label {
            font-size: 0.9rem;
            color: rgba(255, 255, 255, 0.5);
            font-weight: 300;
            letter-spacing: 0.15em;
            text-transform: uppercase;
            display: block;
            margin-bottom: 1rem;
        }

        .section-title {
            font-size: clamp(2rem, 4vw, 3rem);
            color: #ffffff;
            font-weight: 600;
            margin-bottom: 1rem;
            line-height: 1.2;
        }

        .section-subtitle {
            font-size: 1.1rem;
            color: rgba(255, 255, 255, 0.7);
            font-weight: 300;
            line-height: 1.6;
        }

        /* Content styling */
        .legal-content h3 {
            font-size: 1.4rem;
            color: #ffffff;
            font-weight: 600;
            margin: 2rem 0 1rem 0;
            position: relative;
        }

        .legal-content h3::before {
            content: '';
            position: absolute;
            left: -20px;
            top: 50%;
            transform: translateY(-50%);
            width: 3px;
            height: 20px;
            background: linear-gradient(to bottom, rgba(168, 162, 158, 0.8), rgba(168, 162, 158, 0.3));
        }

        .legal-content p {
            color: rgba(255, 255, 255, 0.8);
            margin-bottom: 1.5rem;
            line-height: 1.7;
        }

        .legal-content ul {
            color: rgba(255, 255, 255, 0.8);
            margin: 1.5rem 0;
            padding-left: 2rem;
        }

        .legal-content li {
            margin-bottom: 0.5rem;
            line-height: 1.6;
        }

        .legal-content strong {
            color: #ffffff;
            font-weight: 600;
        }

        /* Contact info styling */
        .contact-info {
            background: rgba(168, 162, 158, 0.02);
            border: 1px solid rgba(168, 162, 158, 0.08);
            border-radius: 8px;
            padding: 2rem;
            margin: 2rem 0;
        }

        .contact-info h4 {
            color: #ffffff;
            font-size: 1.2rem;
            margin-bottom: 1rem;
        }

        .contact-info p {
            margin-bottom: 1rem;
        }

        .contact-info a {
            color: rgba(168, 162, 158, 1);
            text-decoration: none;
            transition: color 0.3s ease;
        }

        .contact-info a:hover {
            color: #ffffff;
        }

        /* Footer */
        .footer {
            padding: 4rem 2rem 2rem;
            text-align: center;
            background: 
                linear-gradient(135deg, rgba(26, 29, 38, 0.98) 0%, rgba(20, 23, 30, 1) 100%),
                linear-gradient(180deg, #1a1d26 0%, #141720 100%);
            border-top: 1px solid rgba(255, 255, 255, 0.1);
        }

        .footer-content {
            max-width: 1200px;
            margin: 0 auto;
        }

        .footer-links {
            display: flex;
            justify-content: center;
            gap: 2rem;
            margin-bottom: 2rem;
            flex-wrap: wrap;
        }

        .footer-links a {
            color: rgba(255, 255, 255, 0.6);
            text-decoration: none;
            transition: color 0.3s ease;
        }

        .footer-links a:hover {
            color: #8b5cf6;
        }

        .copyright {
            color: rgba(255, 255, 255, 0.4);
            font-size: 0.9rem;
        }

        /* Animations */
        .fade-in {
            opacity: 0;
            transform: translateY(20px);
            transition: all 0.8s ease;
        }

        .fade-in.visible {
            opacity: 1;
            transform: translateY(0);
        }

        /* Mobile Responsiveness */
        @media (max-width: 768px) {
            .cursor { display: none; }
            
            .nav-links {
                display: none;
            }
            
            .mobile-menu-btn {
                display: block;
            }
            
            .legal-hero {
                padding: 4rem 1.5rem 3rem;
            }
            
            .legal-section {
                padding: 3rem 1.5rem;
            }
            
            .legal-content h3::before {
                display: none;
            }
        }

        @media (max-width: 480px) {
            .legal-hero {
                padding: 3rem 1rem 2rem;
            }
            
            .legal-section {
                padding: 2rem 1rem;
            }
        }

        /* Email Mask */
		.hide-from-bots { display: none; }
		.email-reveal, .phone-reveal {
			background: #333;
			color: #ccc;
			padding: 8px 15px;
			border-radius: 5px;
			cursor: pointer;
			border: 1px solid #555;
		}
		.email-reveal:hover, .phone-reveal:hover {
			background: #444;
			color: #fff;
		}

        /* Smooth scrolling */
        html {
            scroll-behavior: smooth;
        }

        /* Reduced motion for accessibility */
        @media (prefers-reduced-motion: reduce) {
            * {
                animation-duration: 0.01ms !important;
                animation-iteration-count: 1 !important;
                transition-duration: 0.01ms !important;
            }
        }
    </style>
</head>
<body>
    <div class="cursor"></div>
    
    <!-- Navigation -->
    <nav class="nav" id="nav">
        <div class="nav-container">
            <a href="index.html" class="logo">IMPAXX</a>
            <ul class="nav-links">
                <li><a href="index.html#hero">Accueil</a></li>
                <li><a href="index.html#about">À propos</a></li>
                <li><a href="index.html#services">Services</a></li>
                <li><a href="index.html#contact">Contact</a></li>
            </ul>
            <button class="mobile-menu-btn" id="mobile-menu-btn">☰</button>
        </div>
    </nav>
    
    <main class="main-content">
        <!-- Hero Section -->
        <section class="legal-hero">
            <h1>Informations <span class="accent-word">Légales</span></h1>
            <p>Transparence et confiance au cœur de notre relation d'affaires</p>
        </section>

        <!-- Privacy Policy Section -->
        <section class="legal-section" id="privacy">
            <div class="section-container">
                <div class="section-header fade-in">
                    <span class="section-label">01 — Confidentialité</span>
                    <h2 class="section-title">Politique de Confidentialité</h2>
                    <p class="section-subtitle">IMPAXX s'engage à protéger votre vie privée et vos données personnelles</p>
                </div>
                
                <div class="legal-content fade-in">
                    <h3>Collecte des informations</h3>
                    <p>Nous collectons uniquement les informations nécessaires pour vous fournir nos services de consultation et de transformation digitale. Cela inclut :</p>
                    <ul>
                        <li>Informations de contact (nom, email, téléphone)</li>
                        <li>Informations sur votre entreprise</li>
                        <li>Détails sur vos besoins et défis technologiques</li>
                        <li>Communications lors de nos échanges</li>
                    </ul>

                    <h3>Utilisation des données</h3>
                    <p>Vos informations sont utilisées exclusivement pour :</p>
                    <ul>
                        <li>Vous fournir nos services de consultation</li>
                        <li>Personnaliser nos recommandations</li>
                        <li>Maintenir la communication pendant nos projets</li>
                        <li>Améliorer nos services</li>
                    </ul>

                    <h3>Protection et confidentialité</h3>
                    <p>IMPAXX s'engage à ne <strong>jamais partager, vendre ou distribuer</strong> vos informations personnelles à des tiers. Toutes nos communications et données client sont traitées avec la plus stricte confidentialité.</p>

                    <h3>Conservation des données</h3>
                    <p>Nous conservons vos informations tant que nécessaire pour vous fournir nos services et respecter nos obligations légales. Vous pouvez demander la suppression de vos données à tout moment.</p>

                    <div class="contact-info">
                        <h4>Questions sur la confidentialité ?</h4>
                        <p>Pour toute question concernant cette politique de confidentialité, contactez-nous à :</p>
                        <p><strong>Email :</strong> <span class="email-reveal" onclick="showEmail(this)">📧 Click to show email</span></p>
                    </div>
                </div>
            </div>
        </section>

        <!-- Terms of Service Section -->
        <section class="legal-section" id="terms">
            <div class="section-container">
                <div class="section-header fade-in">
                    <span class="section-label">02 — Conditions</span>
                    <h2 class="section-title">Conditions d'Utilisation</h2>
                    <p class="section-subtitle">Cadre de notre collaboration et de l'utilisation de nos services</p>
                </div>
                
                <div class="legal-content fade-in">
                    <h3>Services offerts</h3>
                    <p>IMPAXX offre des services de consultation en transformation digitale, incluant :</p>
                    <ul>
                        <li>Stratégie digitale et audit technologique</li>
                        <li>Développement de solutions personnalisées</li>
                        <li>Implémentation d'outils d'automatisation et d'IA</li>
                        <li>Accompagnement et formation des équipes</li>
                    </ul>

                    <h3>Engagement mutuel</h3>
                    <p>Notre collaboration repose sur un <strong>engagement mutuel</strong>. Nous nous engageons à fournir une expertise de qualité, et nous attendons de nos clients :</p>
                    <ul>
                        <li>Une communication ouverte et transparente</li>
                        <li>La fourniture des informations nécessaires au projet</li>
                        <li>Le respect des échéanciers convenus</li>
                        <li>Le paiement selon les termes établis</li>
                    </ul>

                    <h3>Propriété intellectuelle</h3>
                    <p>Les solutions développées spécifiquement pour votre entreprise vous appartiennent. IMPAXX conserve les droits sur ses méthodologies et outils génériques utilisés dans le cadre de nos services.</p>

                    <h3>Limitation de responsabilité</h3>
                    <p>IMPAXX s'engage à fournir ses services avec professionnalisme et expertise. Notre responsabilité est limitée au montant des services fournis pour le projet concerné.</p>

                    <h3>Modification des conditions</h3>
                    <p>Ces conditions peuvent être mises à jour pour refléter l'évolution de nos services. Les clients seront informés de tout changement significatif.</p>
                </div>
            </div>
        </section>

        <!-- Support Section -->
        <section class="legal-section" id="support">
            <div class="section-container">
                <div class="section-header fade-in">
                    <span class="section-label">03 — Support</span>
                    <h2 class="section-title">Support Client</h2>
                    <p class="section-subtitle">Nous sommes là pour vous accompagner à chaque étape</p>
                </div>
                
                <div class="legal-content fade-in">
                    <h3>Notre engagement support</h3>
                    <p>Chez IMPAXX, le support ne s'arrête pas à la livraison. Nous croyons en un <strong>accompagnement continu</strong> pour assurer le succès de votre transformation digitale.</p>

                    <h3>Types de support disponibles</h3>
                    <ul>
                        <li><strong>Support technique :</strong> Assistance pour l'utilisation des solutions développées</li>
                        <li><strong>Formation :</strong> Sessions pour vos équipes sur les nouvelles technologies</li>
                        <li><strong>Consultation stratégique :</strong> Conseils pour l'évolution de votre écosystème digital</li>
                        <li><strong>Maintenance :</strong> Mise à jour et optimisation des solutions existantes</li>
                    </ul>

                    <h3>Comment nous contacter</h3>
                    <div class="contact-info">
                        <h4>Besoin d'assistance ?</h4>
                        <p><strong>Email principal :</strong> <span class="email-reveal" onclick="showEmail(this)">📧 Click to show email</span></p>
                        <p><span class="phone-reveal" onclick="showPhone(this)">📞 Click to show phone</span></p>
                        <p><strong>Délai de réponse :</strong> Nous nous engageons à répondre sous 24h pour toute demande de support.</p>
                    </div>

                    <h3>Consultation gratuite</h3>
                    <p>Même si vous n'êtes pas encore client, nous offrons une <strong>consultation gratuite de 30 minutes</strong> pour discuter de vos défis et explorer les opportunités de collaboration.</p>

                    <h3>Ressources disponibles</h3>
                    <p>En plus du support direct, nous fournissons :</p>
                    <ul>
                        <li>Documentation détaillée pour toutes nos solutions</li>
                        <li>Guides d'utilisation adaptés à votre contexte</li>
                        <li>Sessions de formation personnalisées</li>
                        <li>Suivi post-implémentation</li>
                    </ul>

                    <h3>Engagement qualité</h3>
                    <p>Notre approche du support reflète notre philosophie : <strong>penser différemment</strong> pour vous offrir une expérience client exceptionnelle. Chaque interaction est une opportunité d'ajouter de la valeur à votre entreprise.</p>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="footer-content">
            <div class="footer-links">
                <a href="#privacy">Politique de confidentialité</a>
                <a href="#terms">Conditions d'utilisation</a>
                <a href="#support">Support</a>
                <a href="index.html#contact">Retour au Contact</a>
            </div>
            <div class="copyright">
                © 2025 IMPAXX. Tous droits réservés.
            </div>
        </div>
    </footer>

    <script>
        // Interactive cursor
        const cursor = document.querySelector('.cursor');
        let mouseX = 0, mouseY = 0;
        let cursorX = 0, cursorY = 0;
        
        document.addEventListener('mousemove', (e) => {
            mouseX = e.clientX;
            mouseY = e.clientY;
        });
        
        function updateCursor() {
            cursorX += (mouseX - cursorX) * 0.1;
            cursorY += (mouseY - cursorY) * 0.1;
            
            cursor.style.left = cursorX + 'px';
            cursor.style.top = cursorY + 'px';
            
            requestAnimationFrame(updateCursor);
        }
        
        updateCursor();
        
        document.addEventListener('mousedown', () => {
            cursor.classList.add('active');
        });
        
        document.addEventListener('mouseup', () => {
            cursor.classList.remove('active');
        });

        // Smooth scrolling for navigation
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Navbar scroll effect
        window.addEventListener('scroll', () => {
            const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
            const nav = document.getElementById('nav');
            
            if (scrollTop > 100) {
                nav.classList.add('scrolled');
            } else {
                nav.classList.remove('scrolled');
            }
        });

        // Intersection Observer for fade-in animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('visible');
                }
            });
        }, observerOptions);

        document.querySelectorAll('.fade-in').forEach(el => {
            observer.observe(el);
        });

        // Mobile menu
        const mobileMenuBtn = document.getElementById('mobile-menu-btn');
        const navLinks = document.querySelector('.nav-links');
        
        if (mobileMenuBtn && navLinks) {
            mobileMenuBtn.addEventListener('click', () => {
                navLinks.style.display = navLinks.style.display === 'flex' ? 'none' : 'flex';
            });
        }

        // contact elements
		function showEmail(element) {
            element.innerHTML = '<a href="mailto:<EMAIL>" style="color: #ccc;"><EMAIL></a>';
        }
        
        function showPhone(element) {
            element.innerHTML = '<a href="tel:+15148357689" style="color: #ccc;">+****************</a>';
        }
		// Email Submission form
		document.getElementById('contactForm').addEventListener('submit', async function(e) {
		  e.preventDefault();
            }
		});
    </script>
</body>
</html>