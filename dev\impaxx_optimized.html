<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>IMPAXX - Transform Your Digital Future</title>

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    <link rel="apple-touch-icon" href="/icons/apple-icon.png">
    <link rel="manifest" href="/manifest.json">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #0a0a0a;
            min-height: 100vh;
            overflow-x: hidden;
            color: #fff;
            line-height: 1.6;
        }

        /* Main background with better browser support */
        .main-bg {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -2;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 25%, #16213e 50%, #0f3460 75%, #533483 100%);
            opacity: 0.95;
        }

        /* Animated overlay */
        .bg-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
            background: radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
                        radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
                        radial-gradient(circle at 40% 40%, rgba(120, 200, 255, 0.2) 0%, transparent 50%);
            animation: float 20s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            33% { transform: translateY(-30px) rotate(1deg); }
            66% { transform: translateY(-20px) rotate(-1deg); }
        }

        /* Optimized particles */
        .particles {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
            pointer-events: none;
        }

        .particle {
            position: absolute;
            width: 2px;
            height: 2px;
            background: rgba(255, 255, 255, 0.4);
            border-radius: 50%;
            animation: particle-float 8s ease-in-out infinite;
        }

        @keyframes particle-float {
            0%, 100% { transform: translateY(0px) scale(1); opacity: 0.4; }
            50% { transform: translateY(-15px) scale(1.2); opacity: 0.8; }
        }

        /* Navigation */
        .nav {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            background: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            padding: 1rem 0;
            transition: all 0.3s ease;
        }

        .nav.scrolled {
            background: rgba(0, 0, 0, 0.95);
            padding: 0.5rem 0;
        }

        .nav-container {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 2rem;
        }

        .logo {
            font-size: 2rem;
            font-weight: 800;
            color: #fff;
            text-shadow: 0 0 20px rgba(255, 255, 255, 0.5);
            transition: all 0.3s ease;
        }

        .logo:hover {
            transform: scale(1.05);
            text-shadow: 0 0 30px rgba(255, 255, 255, 0.8);
        }

        .nav-links {
            display: flex;
            gap: 2rem;
            list-style: none;
        }

        .nav-links a {
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
            padding: 0.5rem 1rem;
            border-radius: 25px;
            position: relative;
        }

        .nav-links a::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 25px;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .nav-links a:hover::before {
            opacity: 1;
        }

        .nav-links a:hover {
            color: #fff;
            transform: translateY(-2px);
        }

        /* Hero Section */
        .hero {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            padding: 8rem 2rem 4rem;
            position: relative;
        }

        .hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: radial-gradient(circle at center, rgba(255, 255, 255, 0.05) 0%, transparent 70%);
            pointer-events: none;
        }

        .hero-content {
            max-width: 800px;
            animation: hero-fade-in 1.5s ease-out;
            position: relative;
            z-index: 1;
        }

        .hero h1 {
            font-size: clamp(2.5rem, 8vw, 5rem);
            font-weight: 900;
            margin-bottom: 1.5rem;
            color: #fff;
            text-shadow: 0 0 40px rgba(255, 255, 255, 0.3);
            line-height: 1.1;
        }

        .hero .highlight {
            background: linear-gradient(135deg, #8b5cf6, #06b6d4);
            background-clip: text;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            color: #8b5cf6; /* Fallback for unsupported browsers */
        }

        .hero p {
            font-size: 1.3rem;
            margin-bottom: 3rem;
            color: rgba(255, 255, 255, 0.9);
            line-height: 1.7;
        }

        .cta-button {
            display: inline-block;
            padding: 1rem 2.5rem;
            background: linear-gradient(135deg, #8b5cf6, #06b6d4);
            color: white;
            text-decoration: none;
            border-radius: 50px;
            font-weight: 600;
            font-size: 1.1rem;
            transition: all 0.3s ease;
            box-shadow: 0 10px 30px rgba(139, 92, 246, 0.3);
            position: relative;
            overflow: hidden;
        }

        .cta-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.6s ease;
        }

        .cta-button:hover::before {
            left: 100%;
        }

        .cta-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 40px rgba(139, 92, 246, 0.4);
        }

        /* Content sections */
        .section {
            padding: 6rem 2rem;
            position: relative;
        }

        .section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 90%;
            height: 1px;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
        }

        .section-container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .section h2 {
            font-size: 2.5rem;
            margin-bottom: 3rem;
            text-align: center;
            color: #fff;
            text-shadow: 0 0 20px rgba(255, 255, 255, 0.3);
        }

        .about-content {
            max-width: 800px;
            margin: 0 auto;
            text-align: left;
        }

        .about-content p {
            font-size: 1.1rem;
            line-height: 1.8;
            margin-bottom: 2rem;
            color: rgba(255, 255, 255, 0.9);
        }

        .about-content strong {
            color: #8b5cf6;
        }

        /* Services Grid - 2x2 Layout */
        .services-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 2rem;
            margin-top: 3rem;
            max-width: 800px;
            margin-left: auto;
            margin-right: auto;
        }

        /* ENHANCED WOW FACTOR ADDITIONS */
        
        /* 3D Floating Cards Effect */
        .service-card {
            background: rgba(255, 255, 255, 0.08);
            padding: 2.5rem;
            border-radius: 20px;
            border: 1px solid rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(15px);
            -webkit-backdrop-filter: blur(15px);
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            position: relative;
            overflow: hidden;
            transform-style: preserve-3d;
            perspective: 1000px;
        }

        .service-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, 
                rgba(139, 92, 246, 0.2) 0%, 
                rgba(6, 182, 212, 0.2) 50%, 
                rgba(236, 72, 153, 0.2) 100%);
            opacity: 0;
            transition: opacity 0.4s ease;
            border-radius: 20px;
        }

        .service-card::after {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: conic-gradient(
                from 0deg,
                transparent 0deg,
                rgba(139, 92, 246, 0.3) 90deg,
                transparent 180deg,
                rgba(6, 182, 212, 0.3) 270deg,
                transparent 360deg
            );
            animation: rotate 4s linear infinite;
            opacity: 0;
            transition: opacity 0.4s ease;
        }

        @keyframes rotate {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .service-card:hover::before {
            opacity: 1;
        }

        .service-card:hover::after {
            opacity: 0.7;
        }

        .service-card:hover {
            transform: translateY(-20px) rotateX(5deg) rotateY(5deg);
            box-shadow: 
                0 30px 60px rgba(139, 92, 246, 0.3),
                0 0 50px rgba(6, 182, 212, 0.2);
            border-color: rgba(139, 92, 246, 0.5);
        }

        /* Magnetic Hover Effect for Icons */
        .service-icon {
            font-size: 3.5rem;
            margin-bottom: 1rem;
            display: block;
            transition: all 0.3s ease;
            position: relative;
            z-index: 10;
            filter: drop-shadow(0 0 10px rgba(139, 92, 246, 0.5));
        }

        .service-card:hover .service-icon {
            transform: scale(1.2) rotateY(15deg);
            filter: drop-shadow(0 0 20px rgba(139, 92, 246, 0.8));
        }

        /* Glowing Text Effect */
        .service-card h3 {
            font-size: 1.4rem;
            margin-bottom: 1rem;
            color: #fff;
            position: relative;
            z-index: 10;
            text-shadow: 0 0 10px rgba(255, 255, 255, 0.3);
            transition: all 0.3s ease;
        }

        .service-card:hover h3 {
            text-shadow: 0 0 20px rgba(139, 92, 246, 0.8);
            transform: translateZ(20px);
        }

        /* Enhanced Hero Section */
        .hero {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            padding: 8rem 2rem 4rem;
            position: relative;
            background: radial-gradient(circle at 30% 70%, rgba(139, 92, 246, 0.15) 0%, transparent 50%),
                        radial-gradient(circle at 70% 30%, rgba(6, 182, 212, 0.15) 0%, transparent 50%);
        }

        .hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: 
                radial-gradient(circle at 20% 80%, rgba(139, 92, 246, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(6, 182, 212, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(236, 72, 153, 0.08) 0%, transparent 50%);
            animation: pulse 8s ease-in-out infinite;
            pointer-events: none;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }

        /* Morphing CTA Button */
        .cta-button {
            display: inline-block;
            padding: 1.2rem 3rem;
            background: linear-gradient(135deg, #8b5cf6, #06b6d4, #ec4899);
            background-size: 200% 200%;
            color: white;
            text-decoration: none;
            border-radius: 50px;
            font-weight: 700;
            font-size: 1.1rem;
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            box-shadow: 
                0 15px 35px rgba(139, 92, 246, 0.4),
                0 5px 15px rgba(6, 182, 212, 0.3);
            position: relative;
            overflow: hidden;
            transform-style: preserve-3d;
            animation: gradient-shift 3s ease infinite;
        }

        @keyframes gradient-shift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        .cta-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
            transition: left 0.6s ease;
        }

        .cta-button:hover::before {
            left: 100%;
        }

        .cta-button:hover {
            transform: translateY(-8px) scale(1.05);
            box-shadow: 
                0 25px 50px rgba(139, 92, 246, 0.6),
                0 10px 30px rgba(6, 182, 212, 0.4),
                0 0 50px rgba(236, 72, 153, 0.3);
        }

        /* Interactive Background Orbs */
        .orb {
            position: absolute;
            border-radius: 50%;
            background: radial-gradient(circle at 30% 30%, rgba(139, 92, 246, 0.6), rgba(6, 182, 212, 0.4));
            filter: blur(60px);
            animation: float-orb 20s ease-in-out infinite;
            pointer-events: none;
        }

        .orb:nth-child(1) {
            width: 300px;
            height: 300px;
            top: 20%;
            left: 10%;
            animation-delay: 0s;
        }

        .orb:nth-child(2) {
            width: 200px;
            height: 200px;
            top: 60%;
            right: 10%;
            animation-delay: 5s;
        }

        .orb:nth-child(3) {
            width: 150px;
            height: 150px;
            bottom: 20%;
            left: 60%;
            animation-delay: 10s;
        }

        @keyframes float-orb {
            0%, 100% { 
                transform: translateY(0px) translateX(0px) scale(1);
                opacity: 0.6;
            }
            33% { 
                transform: translateY(-30px) translateX(20px) scale(1.1);
                opacity: 0.8;
            }
            66% { 
                transform: translateY(20px) translateX(-15px) scale(0.9);
                opacity: 0.7;
            }
        }

        /* Enhanced Navigation */
        .nav {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            background: rgba(0, 0, 0, 0.7);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            padding: 1rem 0;
            transition: all 0.3s ease;
        }

        .nav::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(90deg, 
                rgba(139, 92, 246, 0.1) 0%, 
                rgba(6, 182, 212, 0.1) 50%, 
                rgba(139, 92, 246, 0.1) 100%);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .nav.scrolled::before {
            opacity: 1;
        }

        /* Logo Enhancement */
        .logo {
            font-size: 2.2rem;
            font-weight: 900;
            background: linear-gradient(135deg, #fff, #8b5cf6, #06b6d4);
            background-clip: text;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            color: #fff; /* Fallback */
            text-shadow: 0 0 30px rgba(139, 92, 246, 0.5);
            transition: all 0.3s ease;
            position: relative;
        }

        .logo::after {
            content: '';
            position: absolute;
            bottom: -5px;
            left: 0;
            width: 100%;
            height: 2px;
            background: linear-gradient(90deg, #8b5cf6, #06b6d4);
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }

        .logo:hover::after {
            transform: scaleX(1);
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .services-grid {
                grid-template-columns: 1fr;
                max-width: 100%;
            }
            
            .service-card:hover {
                transform: translateY(-10px);
            }
            
            .orb {
                display: none; /* Hide orbs on mobile for performance */
            }
        }

        .service-card p {
            color: rgba(255, 255, 255, 0.8);
            line-height: 1.6;
            position: relative;
            z-index: 1;
        }

        /* Footer */
        .footer {
            padding: 4rem 2rem 2rem;
            text-align: center;
            background: rgba(0, 0, 0, 0.5);
            border-top: 1px solid rgba(255, 255, 255, 0.1);
        }

        .footer-content {
            max-width: 1200px;
            margin: 0 auto;
        }

        .footer-links {
            display: flex;
            justify-content: center;
            gap: 2rem;
            margin-bottom: 2rem;
            flex-wrap: wrap;
        }

        .footer-links a {
            color: rgba(255, 255, 255, 0.6);
            text-decoration: none;
            transition: color 0.3s ease;
        }

        .footer-links a:hover {
            color: #8b5cf6;
        }

        .copyright {
            color: rgba(255, 255, 255, 0.4);
            font-size: 0.9rem;
        }

        /* Animations */
        @keyframes hero-fade-in {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .fade-in {
            opacity: 0;
            transform: translateY(20px);
            transition: all 0.8s ease;
        }

        .fade-in.visible {
            opacity: 1;
            transform: translateY(0);
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .nav-links {
                display: none;
            }
            
            .hero {
                padding: 6rem 1rem 2rem;
            }
            
            .section {
                padding: 4rem 1rem;
            }
            
            .services-grid {
                grid-template-columns: 1fr;
            }
            
            .hero h1 {
                font-size: 2.5rem;
            }
            
            .section h2 {
                font-size: 2rem;
            }
        }

        /* Smooth scrolling */
        html {
            scroll-behavior: smooth;
        }

        /* Mobile menu button */
        .mobile-menu-btn {
            display: none;
            background: none;
            border: none;
            color: white;
            font-size: 1.5rem;
            cursor: pointer;
        }

        @media (max-width: 768px) {
            .mobile-menu-btn {
                display: block;
            }
        }

        /* Performance optimizations */
        .service-card,
        .cta-button {
            will-change: transform;
        }

        /* Reduced motion for accessibility */
        @media (prefers-reduced-motion: reduce) {
            * {
                animation-duration: 0.01ms !important;
                animation-iteration-count: 1 !important;
                transition-duration: 0.01ms !important;
            }
        }
    </style>
</head>
<body>
    <!-- Background layers -->
    <div class="main-bg"></div>
    <div class="bg-overlay"></div>
    
    <!-- Interactive Orbs -->
    <div class="orb"></div>
    <div class="orb"></div>
    <div class="orb"></div>
    
    <div class="particles" id="particles"></div>

    <!-- Navigation -->
    <nav class="nav" id="nav">
        <div class="nav-container">
            <div class="logo">IMPAXX</div>
            <ul class="nav-links">
                <li><a href="#hero">Accueil</a></li>
                <li><a href="#apropos">À propos</a></li>
                <li><a href="#services">Services</a></li>
                <li><a href="#contact">Contact</a></li>
            </ul>
            <button class="mobile-menu-btn" id="mobile-menu-btn">☰</button>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero" id="hero">
        <div class="hero-content">
            <h1>Osez <span class="highlight">transformer</span></h1>
            <p>Solutions numériques audacieuses pour PME ambitieuses.<br>
            De la stratégie à l'exécution, IMPAXX vous guide vers une transformation digitale efficace, humaine et durable.</p>
            <a href="#contact" class="cta-button">Démarrons votre projet</a>
        </div>
    </section>

    <!-- About Section -->
    <section class="section" id="apropos">
        <div class="section-container">
            <h2>À propos d'IMPAXX</h2>
            <div class="about-content fade-in">
                <p>
                    Fondée par <strong>Jeff Blain</strong>, expert en technologies avec plus de 25 ans d'expérience, <strong>IMPAXX</strong> est née d'une vision claire : rendre accessible aux PME l'expertise technologique souvent réservée aux grandes entreprises.
                </p>
                <p>
                    Notre approche se distingue par notre <strong>audace</strong>, notre <strong>créativité</strong> et notre engagement à <strong>penser différemment</strong>. Nous ne nous contentons pas de suivre les tendances, nous créons des solutions qui génèrent un véritable impact sur votre croissance et votre compétitivité.
                </p>
                <p>
                    Fort de son parcours dans des multinationales de premier plan (IBM, BRP, CGI, Bell), Jeff a développé une compréhension approfondie des meilleures pratiques qu'il adapte aujourd'hui aux besoins spécifiques des <strong>PME québécoises</strong>.
                </p>
            </div>
        </div>
    </section>

    <!-- Services Section -->
    <section class="section" id="services">
        <div class="section-container">
            <h2>Nos Services</h2>
            <div class="services-grid">
                <div class="service-card fade-in">
                    <div class="service-icon">🎯</div>
                    <h3>Stratégie Digitale</h3>
                    <p>Élaboration d'une vision claire et d'une feuille de route pour votre transformation numérique, en alignement avec vos objectifs d'affaires.</p>
                </div>
                <div class="service-card fade-in">
                    <div class="service-icon">⚙️</div>
                    <h3>Développement de Solutions</h3>
                    <p>Création d'applications, de plateformes et d'outils personnalisés qui répondent parfaitement à vos besoins spécifiques et optimisent vos opérations.</p>
                </div>
                <div class="service-card fade-in">
                    <div class="service-icon">🤖</div>
                    <h3>Automatisation & IA</h3>
                    <p>Implémentation d'outils d'automatisation et d'intelligence artificielle pour optimiser vos processus et prendre des décisions basées sur les données.</p>
                </div>
                <div class="service-card fade-in">
                    <div class="service-icon">📚</div>
                    <h3>Accompagnement & Formation</h3>
                    <p>Support personnalisé et programmes de formation pour assurer une adoption réussie des nouvelles technologies au sein de votre organisation.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer" id="contact">
        <div class="footer-content">
            <div class="footer-links">
                <a href="#privacy">Politique de confidentialité</a>
                <a href="#terms">Conditions d'utilisation</a>
                <a href="#support">Support</a>
                <a href="mailto:<EMAIL>">Contact</a>
            </div>
            <div class="copyright">
                © 2025 IMPAXX. Tous droits réservés.
            </div>
        </div>
    </footer>

    <script>
        // Enhanced particle system with interactive elements
        function createParticles() {
            const particlesContainer = document.getElementById('particles');
            const particleCount = window.innerWidth > 768 ? 40 : 20;
            
            for (let i = 0; i < particleCount; i++) {
                const particle = document.createElement('div');
                particle.className = 'particle';
                particle.style.left = Math.random() * 100 + '%';
                particle.style.top = Math.random() * 100 + '%';
                particle.style.animationDelay = Math.random() * 8 + 's';
                particle.style.animationDuration = (Math.random() * 4 + 6) + 's';
                
                // Add random colors to some particles
                if (Math.random() > 0.7) {
                    const colors = ['rgba(139, 92, 246, 0.6)', 'rgba(6, 182, 212, 0.6)', 'rgba(236, 72, 153, 0.6)'];
                    particle.style.background = colors[Math.floor(Math.random() * colors.length)];
                    particle.style.boxShadow = `0 0 10px ${particle.style.background}`;
                }
                
                particlesContainer.appendChild(particle);
            }
        }

        // Mouse interaction with service cards
        document.addEventListener('mousemove', (e) => {
            const cards = document.querySelectorAll('.service-card');
            const mouseX = e.clientX;
            const mouseY = e.clientY;
            
            cards.forEach(card => {
                const rect = card.getBoundingClientRect();
                const cardX = rect.left + rect.width / 2;
                const cardY = rect.top + rect.height / 2;
                
                const deltaX = mouseX - cardX;
                const deltaY = mouseY - cardY;
                const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);
                
                if (distance < 300) {
                    const intensity = (300 - distance) / 300;
                    const rotateX = (deltaY / 300) * intensity * 10;
                    const rotateY = (deltaX / 300) * intensity * 10;
                    
                    card.style.transform = `translateY(-20px) rotateX(${-rotateX}deg) rotateY(${rotateY}deg)`;
                } else {
                    card.style.transform = '';
                }
            });
        });

        // Parallax effect for orbs
        window.addEventListener('scroll', () => {
            const scrolled = window.pageYOffset;
            const orbs = document.querySelectorAll('.orb');
            
            orbs.forEach((orb, index) => {
                const speed = (index + 1) * 0.3;
                orb.style.transform = `translateY(${scrolled * speed}px)`;
            });
        });

        // Smooth scrolling for navigation
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Navbar scroll effect
        let lastScrollTop = 0;
        window.addEventListener('scroll', () => {
            const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
            const nav = document.getElementById('nav');
            
            if (scrollTop > 100) {
                nav.classList.add('scrolled');
            } else {
                nav.classList.remove('scrolled');
            }
            
            lastScrollTop = scrollTop;
        });

        // Intersection Observer for fade-in animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('visible');
                }
            });
        }, observerOptions);

        // Observe fade-in elements
        document.querySelectorAll('.fade-in').forEach(el => {
            observer.observe(el);
        });

        // Initialize particles when page loads
        document.addEventListener('DOMContentLoaded', () => {
            createParticles();
        });

        // Handle window resize
        window.addEventListener('resize', () => {
            // Recreate particles on resize for better performance
            const particlesContainer = document.getElementById('particles');
            particlesContainer.innerHTML = '';
            createParticles();
        });

        // Mobile menu (basic functionality)
        const mobileMenuBtn = document.getElementById('mobile-menu-btn');
        const navLinks = document.querySelector('.nav-links');
        
        mobileMenuBtn.addEventListener('click', () => {
            navLinks.style.display = navLinks.style.display === 'flex' ? 'none' : 'flex';
        });
    </script>
</body>
</html>